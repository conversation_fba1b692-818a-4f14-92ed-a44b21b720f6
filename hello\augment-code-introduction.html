<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augment Code - 最懂你代码库的AI编程助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #667eea;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #667eea;
        }

        .hero {
            padding: 120px 0 80px;
            text-align: center;
            color: white;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .cta-button {
            display: inline-block;
            background: #ff6b6b;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }

        .features {
            background: white;
            padding: 80px 0;
        }

        .features h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #333;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .feature-card {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s;
            border: 1px solid #e9ecef;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #333;
        }

        .pricing {
            background: #f8f9fa;
            padding: 80px 0;
        }

        .pricing h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #333;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            max-width: 1000px;
            margin: 0 auto;
        }

        .pricing-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            position: relative;
            border: 2px solid #e9ecef;
            transition: all 0.3s;
        }

        .pricing-card.featured {
            border-color: #667eea;
            transform: scale(1.05);
        }

        .pricing-card:hover {
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #333;
        }

        .plan-price {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .plan-features {
            list-style: none;
            margin-bottom: 2rem;
        }

        .plan-features li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .plan-button {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
        }

        .plan-button:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .companies {
            background: white;
            padding: 60px 0;
            text-align: center;
        }

        .companies h3 {
            margin-bottom: 2rem;
            color: #666;
            font-size: 1.2rem;
        }

        .company-logos {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 3rem;
            flex-wrap: wrap;
        }

        .company-logo {
            font-size: 1.5rem;
            color: #999;
            font-weight: bold;
        }

        footer {
            background: #333;
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .pricing-card.featured {
                transform: none;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <div class="logo">🚀 Augment Code</div>
            <ul class="nav-links">
                <li><a href="#features">功能特点</a></li>
                <li><a href="#pricing">价格方案</a></li>
                <li><a href="#companies">客户案例</a></li>
            </ul>
        </nav>
    </header>

    <section class="hero">
        <div class="container">
            <h1>最懂你代码库的AI编程助手</h1>
            <p>基于Claude Sonnet 4的强大AI代理，配备世界领先的上下文引擎，让编程更智能、更高效</p>
            <a href="#pricing" class="cta-button">立即开始14天免费试用</a>
        </div>
    </section>

    <section class="features" id="features">
        <div class="container">
            <h2>🌟 核心功能特点</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🧠</div>
                    <h3>智能上下文引擎</h3>
                    <p>世界领先的代码库理解能力，实时索引你的整个项目，提供精准的代码建议和解决方案</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🤖</div>
                    <h3>Claude Sonnet 4驱动</h3>
                    <p>基于最新的Claude Sonnet 4模型，在代码生成和理解方面表现卓越，质量领先行业</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>无限代码补全</h3>
                    <p>提供无限制的智能代码补全和编辑建议，大幅提升开发效率</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔧</div>
                    <h3>多平台支持</h3>
                    <p>完美兼容VSCode、JetBrains、Vim、GitHub和Slack，无需切换工作环境</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🛡️</div>
                    <h3>企业级安全</h3>
                    <p>SOC 2 Type II认证，付费计划绝不使用您的数据进行AI训练，保护代码隐私</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>行业领先性能</h3>
                    <p>在SWE-bench基准测试中排名第一，代码质量和准确性超越其他AI编程工具</p>
                </div>
            </div>
        </div>
    </section>

    <section class="pricing" id="pricing">
        <div class="container">
            <h2>💰 灵活的价格方案</h2>
            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="plan-name">Community</div>
                    <div class="plan-price">免费</div>
                    <ul class="plan-features">
                        <li>✅ 每月50条用户消息</li>
                        <li>✅ 上下文引擎</li>
                        <li>✅ 无限代码补全</li>
                        <li>✅ 社区支持</li>
                        <li>⚠️ 允许数据训练</li>
                    </ul>
                    <button class="plan-button">立即开始</button>
                </div>
                
                <div class="pricing-card featured">
                    <div class="plan-name">Developer</div>
                    <div class="plan-price">$50<span style="font-size: 1rem;">/月</span></div>
                    <ul class="plan-features">
                        <li>✅ 每月600条用户消息</li>
                        <li>✅ 团队管理(最多100用户)</li>
                        <li>✅ 数据隐私保护</li>
                        <li>✅ SOC 2 Type II认证</li>
                        <li>🎁 14天免费试用</li>
                    </ul>
                    <button class="plan-button">开始试用</button>
                </div>
                
                <div class="pricing-card">
                    <div class="plan-name">Pro</div>
                    <div class="plan-price">$100<span style="font-size: 1rem;">/月</span></div>
                    <ul class="plan-features">
                        <li>✅ 每月1500条用户消息</li>
                        <li>✅ 邮件技术支持</li>
                        <li>✅ 所有Developer功能</li>
                        <li>✅ 增强容量</li>
                    </ul>
                    <button class="plan-button">选择Pro</button>
                </div>
                
                <div class="pricing-card">
                    <div class="plan-name">Max</div>
                    <div class="plan-price">$250<span style="font-size: 1rem;">/月</span></div>
                    <ul class="plan-features">
                        <li>✅ 每月4500条用户消息</li>
                        <li>✅ 高强度使用支持</li>
                        <li>✅ 所有Pro功能</li>
                        <li>✅ 优先技术支持</li>
                    </ul>
                    <button class="plan-button">选择Max</button>
                </div>
            </div>
            <div style="text-align: center; margin-top: 2rem; color: #666;">
                <p>💡 额外消息包：$30/300条消息 | 企业定制方案请联系销售团队</p>
            </div>
        </div>
    </section>

    <section class="companies" id="companies">
        <div class="container">
            <h3>🏢 全球领先企业的信赖之选</h3>
            <div class="company-logos">
                <div class="company-logo">Webflow</div>
                <div class="company-logo">Kong</div>
                <div class="company-logo">GoFundMe</div>
                <div class="company-logo">Lemonade</div>
                <div class="company-logo">Pure Storage</div>
                <div class="company-logo">DDN</div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>&copy; 2025 Augment Code. 让AI编程更智能，让开发更高效。</p>
            <p style="margin-top: 1rem; opacity: 0.8;">
                🌐 官网：<a href="https://www.augmentcode.com" style="color: #667eea;">www.augmentcode.com</a> | 
                📧 联系：<EMAIL>
            </p>
        </div>
    </footer>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // 滚动时头部透明度变化
        window.addEventListener('scroll', function() {
            const header = document.querySelector('header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
            }
        });
    </script>
</body>
</html>
