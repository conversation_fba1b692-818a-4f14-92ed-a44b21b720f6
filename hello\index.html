<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>test</title>
</head>
<body>
    <h1>Hello World</h1>
    <div class="todo-app">
        <div class="title">xxx的Todo App</div>
        <div class="todo-form">
            <input class="todo-input" type="text" placeholder="Add a todo"/>
            <div class="todo-button">Add Todo</div>

        </div>
    </div>
    
    <style> 
       body {
        /* 线性渐变方向：从左到右 */
        background: linear-gradient(to right, rgb(113, 65, 168), rgba(44, 114, 251,1));
        /* 确保背景覆盖整个视口（可选） */
        margin: 0;
        min-height: 100vh;
        /* 固定渐变背景不随内容滚动（可选） */
        background-attachment: fixed;
        }
        .todo-app{
            width: 98%;
            height: 500px;
            background-color:#ffff;
            padding-top: 30px;
            box-sizing: border-box;
            border-radius:5px;
            margin-top: 40px;
            margin-left: 1%;
          }
        .title{
            font-size: 30px;
            font-weight: 700;
            text-align: center;

        }
        .todo-form{
            display: flex;
            margin-top: 20px;
            margin-left: 30px;

        }
        .todo-input{
            border: 1px solid #dfdfdf;
            outline: none;
            width: 60%;
            height: 50px;
            border-radius: 20px 0 0 20px;
            padding: left 15px;
        }
        .todo-button{
            width:100px;
            height:52px;
            border-radius:0 20px20px 0;
            text-align:center;
            background:linear-gradient(to right,rgb(113,65,168),rgba(44,114,251,1));
            color: #ffff;
            line-height: 52px;
            cursor: pointer;
            user-select: none;

        }


    </style>
     
</body>
</html>